# 文档目录功能使用说明

## 功能概述

在 `FileOperationService` 中新增了获取文档目录页中所有目录的功能，支持在文档拆分时同时提取目录信息。

## 新增的方法

### 1. `getDocumentTableOfContents(MultipartFile file)`

**功能**: 单独获取文档目录页中的所有目录信息

**参数**: 
- `file`: 上传的Word文档文件

**返回值**: `List<JSONObject>` - 包含目录信息的JSON对象列表

**返回数据结构**:
```json
[
  {
    "title": "第一章 总则",
    "level": 1,
    "pageNumber": "1",
    "index": 15
  },
  {
    "title": "1.1 目的",
    "level": 2,
    "pageNumber": "1",
    "index": 18
  }
]
```

### 2. `splitDocumentByHeadingsWithTOC(MultipartFile file, Integer templateId, Integer formdataId)`

**功能**: 按标题切分Word文档，同时提取文档目录信息

**参数**: 
- `file`: 上传的Word文档文件
- `templateId`: 模板ID
- `formdataId`: 表单数据ID

**返回值**: `JSONObject` - 包含拆分文档和目录信息的JSON对象

**返回数据结构**:
```json
{
  "splitDocuments": [
    {
      "fileName": "第一章总则-0",
      "filePath": "123/456/第一章总则-0.docx"
    }
  ],
  "tableOfContents": [
    {
      "title": "第一章 总则",
      "level": 1,
      "pageNumber": "1",
      "index": 15
    }
  ],
  "totalSections": 5
}
```

### 3. `splitDocumentByHeadings(MultipartFile file, Integer templateId, Integer formdataId)` (保持兼容)

**功能**: 原有的文档拆分方法，保持向后兼容

**返回值**: `List<JSONObject>` - 只返回拆分后的文档列表

## 新增的API接口

### 1. `/fileOperation/splitWithTOC` (POST)

**功能**: 文档结构化（包含目录信息）

**请求体**: `FileSplitRequest`
```json
{
  "templateId": 123,
  "formdataId": 456,
  "subTemplateId": 789
}
```

**响应**:
```json
{
  "success": true,
  "result": {
    "message": "文档结构化完成",
    "splitDocuments": [...],
    "tableOfContents": [...],
    "totalSections": 5
  }
}
```

### 2. `/fileOperation/getTableOfContents` (POST)

**功能**: 获取文档目录信息

**请求体**: `FileSplitRequest`
```json
{
  "templateId": 123,
  "formdataId": 456
}
```

**响应**:
```json
{
  "success": true,
  "result": {
    "tableOfContents": [...],
    "totalItems": 10
  }
}
```

## 目录信息字段说明

- `title`: 目录标题文本
- `level`: 标题级别 (1-6，对应标题1到标题6)
- `pageNumber`: 页码信息（如果文档中包含页码）
- `index`: 段落在文档中的索引位置

## 支持的标题样式

系统能够识别以下标题样式：
- WPS Office的数字样式ID (4=标题1, 5=标题2, 6=标题3, 7=标题4)
- 标准的Heading样式 (Heading 1-6)
- 中文标题样式 (标题1-6)
- 级别标题样式 (一级标题、二级标题等)

## 使用示例

### 前端调用示例

```javascript
// 获取文档目录信息
const getTOC = async (templateId, formdataId) => {
  const response = await fetch('/fileOperation/getTableOfContents', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总计目录项:', result.result.totalItems);
  }
};

// 文档拆分并获取目录
const splitWithTOC = async (templateId, formdataId, subTemplateId) => {
  const response = await fetch('/fileOperation/splitWithTOC', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId,
      subTemplateId: subTemplateId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('拆分文档:', result.result.splitDocuments);
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总段落数:', result.result.totalSections);
  }
};
```

## 注意事项

1. 目前只支持 `.docx` 格式的Word文档
2. 页码提取依赖于文档中的格式，可能在某些情况下无法准确提取
3. 标题级别识别基于Word文档的样式信息
4. 建议在使用前确保文档具有正确的标题样式格式

## 日志信息

系统会记录详细的处理日志，包括：
- 发现的段落总数
- 识别的目录项数量
- 每个目录项的详细信息
- 处理过程中的错误信息

可以通过查看应用日志来了解处理过程和排查问题。
