# 文档目录页解析功能使用说明

## 功能概述

在 `FileOperationService` 中新增了专门解析文档目录页的功能，能够从Word文档的目录页中提取目录项及其层级关系，而不是基于文档中的标题样式。该功能专门针对包含"目次"或"目录"页面的正式文档。

## 新增的方法

### 1. `getDocumentTableOfContents(MultipartFile file)`

**功能**: 专门解析文档目录页中的所有目录信息及层级关系

**参数**:
- `file`: 上传的Word文档文件

**返回值**: `List<JSONObject>` - 包含目录信息的JSON对象列表

**解析原理**:
- 自动查找文档中的"目次"、"目录"或"TABLE OF CONTENTS"页面
- 解析目录页中的每一行，提取目录项、层级和页码
- 通过缩进、编号模式等判断目录项的层级关系

**返回数据结构**:
```json
[
  {
    "title": "前言",
    "level": 1,
    "pageNumber": "II",
    "index": 5,
    "originalText": "前言 ................................................................ II"
  },
  {
    "title": "1 范围",
    "level": 1,
    "pageNumber": "1",
    "index": 8,
    "originalText": "1 范围 ................................................................ 1"
  },
  {
    "title": "6.1 设备分级",
    "level": 2,
    "pageNumber": "2",
    "index": 15,
    "originalText": "  6.1 设备分级 .................................................... 2"
  }
]
```

### 2. `splitDocumentByHeadingsWithTOC(MultipartFile file, Integer templateId, Integer formdataId)`

**功能**: 按标题切分Word文档，同时提取文档目录信息

**参数**: 
- `file`: 上传的Word文档文件
- `templateId`: 模板ID
- `formdataId`: 表单数据ID

**返回值**: `JSONObject` - 包含拆分文档和目录信息的JSON对象

**返回数据结构**:
```json
{
  "splitDocuments": [
    {
      "fileName": "第一章总则-0",
      "filePath": "123/456/第一章总则-0.docx"
    }
  ],
  "tableOfContents": [
    {
      "title": "第一章 总则",
      "level": 1,
      "pageNumber": "1",
      "index": 15
    }
  ],
  "totalSections": 5
}
```

### 3. `splitDocumentByHeadings(MultipartFile file, Integer templateId, Integer formdataId)` (保持兼容)

**功能**: 原有的文档拆分方法，保持向后兼容

**返回值**: `List<JSONObject>` - 只返回拆分后的文档列表

## 新增的API接口

### 1. `/fileOperation/splitWithTOC` (POST)

**功能**: 文档结构化（包含目录信息）

**请求体**: `FileSplitRequest`
```json
{
  "templateId": 123,
  "formdataId": 456,
  "subTemplateId": 789
}
```

**响应**:
```json
{
  "success": true,
  "result": {
    "message": "文档结构化完成",
    "splitDocuments": [...],
    "tableOfContents": [...],
    "totalSections": 5
  }
}
```

### 2. `/fileOperation/getTableOfContents` (POST)

**功能**: 获取文档目录信息

**请求体**: `FileSplitRequest`
```json
{
  "templateId": 123,
  "formdataId": 456
}
```

**响应**:
```json
{
  "success": true,
  "result": {
    "tableOfContents": [...],
    "totalItems": 10
  }
}
```

## 目录信息字段说明

- `title`: 目录标题文本（已清理格式，去除点线和页码）
- `level`: 目录层级 (1-4，根据缩进和编号模式判断)
- `pageNumber`: 页码信息（支持阿拉伯数字和罗马数字）
- `index`: 目录项在目录页中的索引位置
- `originalText`: 原始目录行文本（保留完整格式）

## 支持的目录格式

系统能够识别以下目录格式：

### 编号模式
- **一级目录**: `1 范围`, `第一章 总则`, `前言`, `附录`
- **二级目录**: `6.1 设备分级`, `1.1 目的`
- **三级目录**: `6.1.1 具体要求`
- **四级目录**: `******* 详细说明`

### 页码格式
- **阿拉伯数字**: `1`, `2`, `10`
- **罗马数字**: `I`, `II`, `III`, `IV`

### 连接符号
- **点线**: `....`, `…………`
- **中文省略号**: `···`

### 缩进识别
- 通过前导空格数量判断层级关系
- 支持Tab缩进和空格缩进

## 使用示例

### 前端调用示例

```javascript
// 获取文档目录信息
const getTOC = async (templateId, formdataId) => {
  const response = await fetch('/fileOperation/getTableOfContents', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总计目录项:', result.result.totalItems);
  }
};

// 文档拆分并获取目录
const splitWithTOC = async (templateId, formdataId, subTemplateId) => {
  const response = await fetch('/fileOperation/splitWithTOC', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      templateId: templateId,
      formdataId: formdataId,
      subTemplateId: subTemplateId
    })
  });
  
  const result = await response.json();
  if (result.success) {
    console.log('拆分文档:', result.result.splitDocuments);
    console.log('目录信息:', result.result.tableOfContents);
    console.log('总段落数:', result.result.totalSections);
  }
};
```

## 注意事项

1. **文档格式**: 目前只支持 `.docx` 格式的Word文档
2. **目录页识别**: 系统会自动查找包含"目次"、"目录"或"TABLE OF CONTENTS"的页面
3. **层级判断**: 通过缩进、编号模式等多种方式综合判断目录项的层级关系
4. **页码提取**: 支持阿拉伯数字和罗马数字页码，位于行末
5. **文本清理**: 自动去除目录项中的点线连接符和多余空格
6. **容错处理**: 对于格式不规范的目录项，系统会尽量解析并记录原始文本

## 目录页识别规则

系统通过以下方式识别目录页：
1. 查找包含"目次"、"目录"、"TABLE OF CONTENTS"的段落作为起始点
2. 从起始点开始收集后续段落，直到遇到明显的章节开始标志
3. 自动过滤空行和标题行
4. 限制目录页段落数量在合理范围内（最多50个段落）

## 日志信息

系统会记录详细的处理日志，包括：
- 发现的段落总数
- 识别的目录项数量
- 每个目录项的详细信息
- 处理过程中的错误信息

可以通过查看应用日志来了解处理过程和排查问题。
