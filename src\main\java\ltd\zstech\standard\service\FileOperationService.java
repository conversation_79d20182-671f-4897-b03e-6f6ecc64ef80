package ltd.zstech.standard.service;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.standard.entity.DocumentSection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
public class FileOperationService {

    @Value("${jeecg.path.upload}")
    private String path;

    @Resource
    private FormOperationService formOperationService;

    static {
        registerWord2412();
    }

    /**
     * 按标题切分Word文档（改进版，专门处理WPS文档的样式和编号问题）
     */
    public List<JSONObject> splitDocumentByHeadings(MultipartFile file, Integer templateId, Integer formdataId) {
        List<JSONObject> result = new ArrayList<>();
        try {
            // 加载源文档
            Document sourceDoc = new Document(file.getInputStream());
            // 获取文档中的所有Body节点（包含段落和表格等）
            NodeCollection bodies = sourceDoc.getChildNodes(NodeType.BODY, true);
            List<DocumentSection> sections = new ArrayList<>();
            List<Node> allBodyNodes = new ArrayList<>();
            List<Paragraph> headingParagraphs = new ArrayList<>();

            // 收集所有Body中的直接子节点（段落、表格等）
            for (int bodyIndex = 0; bodyIndex < bodies.getCount(); bodyIndex++) {
                Body body = (Body) bodies.get(bodyIndex);
                NodeCollection bodyChildren = body.getChildNodes(NodeType.ANY, false);

                for (int i = 0; i < bodyChildren.getCount(); i++) {
                    Node node = bodyChildren.get(i);
                    allBodyNodes.add(node);

                    // 如果是段落，检查是否为标题
                    if (node.getNodeType() == NodeType.PARAGRAPH) {
                        Paragraph para = (Paragraph) node;
                        log.info("样式：{} - 内容：{}", para.getParagraphFormat().getStyle().getName(), para.getText().trim());
                        if (isHeadingLevel(para)) {
                            headingParagraphs.add(para);
                        }
                    } else if (node.getNodeType() == NodeType.TABLE) {
                        log.info("发现表格节点");
                    }
                }
            }

            // 处理首页内容（第一个标题之前的所有内容）
            if (!headingParagraphs.isEmpty()) {
                Paragraph firstHeading = headingParagraphs.get(0);
                int firstHeadingIndex = allBodyNodes.indexOf(firstHeading);

                if (firstHeadingIndex > 0) {
                    List<Node> frontPageContent = new ArrayList<>();
                    // 收集第一个标题之前的所有内容作为首页
                    for (int j = 0; j < firstHeadingIndex; j++) {
                        frontPageContent.add(allBodyNodes.get(j));
                    }

                    if (!frontPageContent.isEmpty()) {
                        DocumentSection frontPageSection = new DocumentSection("首页", null, frontPageContent);
                        sections.add(frontPageSection);
                        log.info("创建首页段落，包含 {}个节点", frontPageContent.size());
                    }
                }
            }

            // 为每个标题创建段落
            for (int i = 0; i < headingParagraphs.size(); i++) {
                Paragraph headingPara = headingParagraphs.get(i);
                List<Node> sectionContent = new ArrayList<>();

                // 找到当前标题在所有节点中的位置
                int startIndex = allBodyNodes.indexOf(headingPara);
                if (startIndex == -1) continue;

                // 添加当前标题段落
                sectionContent.add(headingPara);

                // 查找下一个标题的位置
                int nextHeadingIndex = allBodyNodes.size();
                if (i < headingParagraphs.size() - 1) {
                    Paragraph nextHeading = headingParagraphs.get(i + 1);
                    int nextIndex = allBodyNodes.indexOf(nextHeading);
                    if (nextIndex != -1) {
                        nextHeadingIndex = nextIndex;
                    }
                }

                // 添加从当前标题后到下一个标题前的所有节点（包括表格）
                for (int j = startIndex + 1; j < nextHeadingIndex; j++) {
                    sectionContent.add(allBodyNodes.get(j));
                }

                DocumentSection section = new DocumentSection(headingPara.getText().trim(), headingPara, sectionContent);
                sections.add(section);

                log.info("创建段落: {}, 包含 {}个节点", headingPara.getText().trim(), sectionContent.size());
            }

            // 生成拆分后的文档
            result = createSplitDocuments(sections, sourceDoc, templateId, formdataId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("文档拆分完成");
        return result;
    }

    private List<JSONObject> createSplitDocuments(List<DocumentSection> sections, Document originalDoc, Integer templateId, Integer formdataId) throws Exception {
        log.info("开始创建 {} 个拆分文档", sections.size());
        List<JSONObject> result = new ArrayList<>();
        
        // 预先分析原文档中的编号信息
        java.util.Map<Integer, Integer> listStartNumbers = new java.util.HashMap<>();
        analyzeListNumbering(originalDoc, sections, listStartNumbers);
        
        for (int i = 0; i < sections.size(); i++) {
            try {
                log.info("正在处理第 {} 个段落: {}", i + 1, sections.get(i).Title);

                // 创建新文档
                Document newDoc = new Document();
                newDoc.removeAllChildren();

                // 复制原文档的所有样式（包括表格样式）
                copyAllStyles(originalDoc, newDoc);

                // 创建新的段落
                Section newSection = new Section(newDoc);
                Body newBody = new Body(newDoc);
                newSection.appendChild(newBody);
                newDoc.appendChild(newSection);

                // 确定内容范围
                List<Node> contentNodes = getSectionContent(sections, i);
                log.info("段落包含 {} 个节点", contentNodes.size());

                // 复制内容到新文档
                for (Node node : contentNodes) {
                    try {
                        log.info("正在导入节点类型: {}", node.getNodeType());
                        Node importedNode = newDoc.importNode(node, true);
                        newBody.appendChild(importedNode);

                        if (node.getNodeType() == NodeType.TABLE) {
                            log.info("成功导入表格节点");
                        }
                    } catch (Exception ex) {
                        log.info("节点 {} 导入失败: {}", node.getNodeType(), ex.getMessage());
                        ex.printStackTrace();
                    }
                }
                
                // 修复编号问题：设置正确的列表起始编号
                fixListNumbering(newDoc, sections.get(i), listStartNumbers, i);

                // 生成文件名
                String fileName = sanitizeFileName(sections.get(i).Title + " - " + i);
                String filePath = Paths.get(path + File.separator + templateId + File.separator + formdataId, fileName + ".docx").toString();

                // 保存文档
                newDoc.save(filePath);
                log.info("已保存: {}", filePath);

                // 清理内存
                contentNodes.clear();
                contentNodes = null;

                JSONObject obj = new JSONObject();
                obj.put("fileName", fileName);
                obj.put("filePath", templateId + "/" + formdataId + "/" + fileName + ".docx");
                result.add(obj);
            } catch (Exception ex) {
                log.info("处理第 {} 个段落时出错: {}", i + 1, ex.getMessage());
                ex.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 分析原文档中的列表编号信息
     */
    private void analyzeListNumbering(Document originalDoc, List<DocumentSection> sections, java.util.Map<Integer, Integer> listStartNumbers) {
        try {
            // 遍历所有段落，记录每个列表的当前编号状态
            java.util.Map<Integer, Integer> currentListNumbers = new java.util.HashMap<>();
            
            for (int sectionIndex = 0; sectionIndex < sections.size(); sectionIndex++) {
                DocumentSection section = sections.get(sectionIndex);
                int sectionStartNumber = -1;
                
                for (Node node : section.Content) {
                    if (node.getNodeType() == NodeType.PARAGRAPH) {
                        Paragraph para = (Paragraph) node;
                        
                        // 检查段落是否有列表格式
                        if (para.getListFormat().isListItem()) {
                            int listId = para.getListFormat().getList().getListId();
                            int currentLevel = para.getListFormat().getListLevelNumber();
                            
                            // 获取当前段落的编号值
                            try {
                                // 尝试获取实际的编号值
                                String listText = para.getListLabel().getLabelString();
                                if (listText.matches("\\d+.*")) {
                                    int number = Integer.parseInt(listText.replaceAll("[^0-9]", ""));
                                    
                                    if (sectionStartNumber == -1) {
                                        // 记录这个段落中第一个编号项的起始编号
                                        sectionStartNumber = number;
                                        listStartNumbers.put(sectionIndex, number);
                                        log.info("段落 {} 的起始编号: {}", section.Title, number);
                                    }
                                    
                                    currentListNumbers.put(listId, number);
                                }
                            } catch (Exception e) {
                                log.debug("无法获取段落编号: {}", e.getMessage());
                            }
                        }
                    }
                }
                
                // 如果段落没有编号，设置为1
                if (sectionStartNumber == -1) {
                    listStartNumbers.put(sectionIndex, 1);
                }
            }
        } catch (Exception e) {
            log.error("分析列表编号时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 修复新文档中的列表编号
     */
    private void fixListNumbering(Document newDoc, DocumentSection section, java.util.Map<Integer, Integer> listStartNumbers, int sectionIndex) {
        try {
            Integer startNumber = listStartNumbers.get(sectionIndex);
            
            if (startNumber == null || startNumber <= 1) {
                return; // 不需要修复
            }
            
            log.info("修复段落 {} 的编号，起始编号: {}", section.Title, startNumber);
            
            // 遍历新文档中的所有段落
            NodeCollection paragraphs = newDoc.getChildNodes(NodeType.PARAGRAPH, true);
            boolean isFirstNumberedItem = true;
            
            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph para = (Paragraph) paragraphs.get(i);
                
                if (para.getListFormat().isListItem()) {
                    try {
                        // 获取列表和级别
                        com.aspose.words.List list = para.getListFormat().getList();
                        int levelNumber = para.getListFormat().getListLevelNumber();
                        
                        if (isFirstNumberedItem) {
                            // 对于第一个编号项，设置起始编号
                            ListLevel listLevel = list.getListLevels().get(levelNumber);
                            listLevel.setStartAt(startNumber);
                            isFirstNumberedItem = false;
                            log.info("设置列表级别 {} 的起始编号为: {}", levelNumber, startNumber);
                        }
                    } catch (Exception e) {
                        log.debug("设置段落编号时出错: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.error("修复列表编号时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 复制所有样式（包括表格样式）
     */
    private void copyAllStyles(Document sourceDoc, Document targetDoc) {
        try {
            // 复制所有样式
            for (Style style : sourceDoc.getStyles()) {
                try {
                    // 检查目标文档中是否已存在该样式
                    boolean exists = false;
                    for (Style existingStyle : targetDoc.getStyles()) {
                        if (existingStyle.getName().equals(style.getName())) {
                            exists = true;
                            break;
                        }
                    }

                    if (!exists) {
                        targetDoc.getStyles().addCopy(style);
                        log.info("复制样式: {}", style.getName());
                    }
                } catch (Exception ex) {
                    log.info("样式{}复制失败: {}", style.getName(), ex.getMessage());
                }
            }

            // 复制列表定义（用于编号和项目符号）
            for (int i = 0; i < sourceDoc.getLists().getCount(); i++) {
                try {
                    targetDoc.getLists().addCopy(sourceDoc.getLists().get(i));
                } catch (Exception ex) {
                    log.info("列表定义复制失败: {}", ex.getMessage());
                }
            }

        } catch (Exception ex) {
            log.info("样式复制过程中出错: {}", ex.getMessage());
        }
    }

    private List<Node> getSectionContent(List<DocumentSection> sections, int currentIndex) {
        // 直接返回已经收集好的内容
        return sections.get(currentIndex).Content;
    }


    /**
     * 清理文件名
     */
    private String sanitizeFileName(String fileName) {
        fileName = fileName.replaceAll(" ", "");
        return fileName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_")
                .replaceAll("_{2,}", "_")
                .substring(0, Math.min(fileName.length(), 50));
    }

    /**
     * 检查是否为标题样式
     */
    private boolean isHeadingLevel(Paragraph para) {
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style == null) {
                return false;
            }

            String styleName = style.getName().toLowerCase();

            // 1. 匹配WPS Office的数字样式ID（4=标题1, 5=标题2, 6=标题3, 7=标题4）
            if (styleName.matches("[4-9]") ||
                    styleName.matches("heading\\s*[1-6]") ||
                    styleName.matches(".*标题\\s*[1-6].*") ||
                    styleName.matches(".*[一级二三四五六]级标题.*") ||
                    styleName.matches(".*title\\s*[1-6].*") ||
                    styleName.matches("heading[1-6]") ||
                    styleName.matches(".*标题[1-6].*")) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 合并文档
     *
     * @param templateId
     * @param formdataId
     */
    public void mergeDocuments(Integer templateId, Integer formdataId) {
        try {
            String sourcePath = path + File.separator + templateId + File.separator + formdataId;
            String targetPath = path + File.separator + templateId;

            File dir = new File(sourcePath);
            File[] docxFiles = dir.listFiles((d, name) -> name.toLowerCase().endsWith(".docx"));
            if (docxFiles == null || docxFiles.length == 0) {
                log.info("未找到任何.docx文件");
                return;
            }

            // 按文件名排序，确保合并顺序正确
            Arrays.sort(docxFiles, Comparator.comparing((File file) -> {
                String name = file.getName();
                int dashIndex = name.lastIndexOf("-");
                if (dashIndex != -1 && dashIndex < name.length() - 1) {
                    try {
                        String numStr = name.substring(dashIndex + 1, name.lastIndexOf("."));
                        return Integer.parseInt(numStr);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，按文件名排序
                        return Integer.MAX_VALUE;
                    }
                }
                return Integer.MAX_VALUE;
            }));

            // 打印排序后的文件列表
            log.info("找到以下文档，将按以下顺序合并：");
            for (int i = 0; i < docxFiles.length; i++) {
                System.out.println((i + 1) + ". " + docxFiles[i].getName());
                log.info("{}.{}", i + 1, docxFiles[i].getName());
            }

            // 初始化 Document 对象
            Document doc = new Document(docxFiles[0].getAbsolutePath());
            log.info("基础文档: {}", docxFiles[0].getName());

            // 从第二个文件开始合并（跳过第一个，因为它已经是基础文档）
            for (int i = 1; i < docxFiles.length; i++) {
                Document document = new Document(docxFiles[i].getAbsolutePath());
                System.out.println("正在合并: " + docxFiles[i].getName());

                // 判断是否需要分页：目录(0)和前言(1)之间需要分页
                boolean needsPageBreak = (i == 1); // 只在前言文档前添加分页

                // 使用改进的合并方法
                appendDocument(doc, document, needsPageBreak);
            }

            // 保存合并的文档，使用时间戳避免文件占用
            String finalOutputPath = targetPath + File.separator + "result.docx";
            doc.save(finalOutputPath);
            System.out.println("文档合并完成，保存至: " + finalOutputPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void appendDocument(Document dstDoc, Document srcDoc, boolean needsPageBreak) throws Exception {
        if (needsPageBreak) {
            // 保留分页符：设置为新页开始
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.NEW_PAGE);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.KEEP_SOURCE_FORMATTING);
        } else {
            // 连续合并：设置为连续模式，避免分页
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.CONTINUOUS);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.USE_DESTINATION_STYLES);
        }
    }


    public static void registerWord2412() {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
