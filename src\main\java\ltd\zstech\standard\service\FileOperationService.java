package ltd.zstech.standard.service;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.*;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.standard.entity.DocumentSection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
public class FileOperationService {

    @Value("${jeecg.path.upload}")
    private String path;

    @Resource
    private FormOperationService formOperationService;

    static {
        registerWord2412();
    }

    /**
     * 按标题切分Word文档（改进版，专门处理WPS文档的样式和编号问题）
     * 同时提取文档目录信息
     */
    public JSONObject splitDocumentByHeadingsWithTOC(MultipartFile file, Integer templateId, Integer formdataId) {
        JSONObject result = new JSONObject();
        List<JSONObject> splitDocuments = new ArrayList<>();
        List<JSONObject> tableOfContents = new ArrayList<>();

        try {
            // 加载源文档
            Document sourceDoc = new Document(file.getInputStream());

            // 首先提取目录信息
            JSONObject tocResult = extractTableOfContents(sourceDoc);
            tableOfContents = tocResult.getJSONArray("flatToc").toJavaList(JSONObject.class);

            // 获取文档中的所有Body节点（包含段落和表格等）
            NodeCollection bodies = sourceDoc.getChildNodes(NodeType.BODY, true);
            List<DocumentSection> sections = new ArrayList<>();
            List<Node> allBodyNodes = new ArrayList<>();
            List<Paragraph> headingParagraphs = new ArrayList<>();

            // 收集所有Body中的直接子节点（段落、表格等）
            for (int bodyIndex = 0; bodyIndex < bodies.getCount(); bodyIndex++) {
                Body body = (Body) bodies.get(bodyIndex);
                NodeCollection bodyChildren = body.getChildNodes(NodeType.ANY, false);

                for (int i = 0; i < bodyChildren.getCount(); i++) {
                    Node node = bodyChildren.get(i);
                    allBodyNodes.add(node);

                    // 如果是段落，检查是否为标题
                    if (node.getNodeType() == NodeType.PARAGRAPH) {
                        Paragraph para = (Paragraph) node;
                        log.info("样式：{} - 内容：{}", para.getParagraphFormat().getStyle().getName(), para.getText().trim());
                        if (isHeadingLevel(para)) {
                            headingParagraphs.add(para);
                        }
                    } else if (node.getNodeType() == NodeType.TABLE) {
                        log.info("发现表格节点");
                    }
                }
            }

            // 处理首页内容（第一个标题之前的所有内容）
            if (!headingParagraphs.isEmpty()) {
                Paragraph firstHeading = headingParagraphs.get(0);
                int firstHeadingIndex = allBodyNodes.indexOf(firstHeading);

                if (firstHeadingIndex > 0) {
                    List<Node> frontPageContent = new ArrayList<>();
                    // 收集第一个标题之前的所有内容作为首页
                    for (int j = 0; j < firstHeadingIndex; j++) {
                        frontPageContent.add(allBodyNodes.get(j));
                    }

                    if (!frontPageContent.isEmpty()) {
                        DocumentSection frontPageSection = new DocumentSection("首页", null, frontPageContent);
                        sections.add(frontPageSection);
                        log.info("创建首页段落，包含 {}个节点", frontPageContent.size());
                    }
                }
            }

            // 为每个标题创建段落
            for (int i = 0; i < headingParagraphs.size(); i++) {
                Paragraph headingPara = headingParagraphs.get(i);
                List<Node> sectionContent = new ArrayList<>();

                // 找到当前标题在所有节点中的位置
                int startIndex = allBodyNodes.indexOf(headingPara);
                if (startIndex == -1) continue;

                // 添加当前标题段落
                sectionContent.add(headingPara);

                // 查找下一个标题的位置
                int nextHeadingIndex = allBodyNodes.size();
                if (i < headingParagraphs.size() - 1) {
                    Paragraph nextHeading = headingParagraphs.get(i + 1);
                    int nextIndex = allBodyNodes.indexOf(nextHeading);
                    if (nextIndex != -1) {
                        nextHeadingIndex = nextIndex;
                    }
                }

                // 添加从当前标题后到下一个标题前的所有节点（包括表格）
                for (int j = startIndex + 1; j < nextHeadingIndex; j++) {
                    sectionContent.add(allBodyNodes.get(j));
                }

                DocumentSection section = new DocumentSection(headingPara.getText().trim(), headingPara, sectionContent);
                sections.add(section);

                log.info("创建段落: {}, 包含 {}个节点", headingPara.getText().trim(), sectionContent.size());
            }

            // 生成拆分后的文档
            splitDocuments = createSplitDocuments(sections, sourceDoc, templateId, formdataId);

            // 组装返回结果
            result.put("splitDocuments", splitDocuments);
            result.put("tableOfContents", tableOfContents);
            result.put("totalSections", sections.size());

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info("文档拆分完成，共生成 {} 个文档，提取 {} 个目录项", splitDocuments.size(), tableOfContents.size());
        return result;
    }

    /**
     * 按标题切分Word文档（原版方法，保持向后兼容）
     */
    public List<JSONObject> splitDocumentByHeadings(MultipartFile file, Integer templateId, Integer formdataId) {
        JSONObject result = splitDocumentByHeadingsWithTOC(file, templateId, formdataId);
        return result.getJSONArray("splitDocuments").toJavaList(JSONObject.class);
    }

    /**
     * 从文档中提取目录信息（基于标题样式，用于文档拆分）
     */
    private JSONObject extractTableOfContents(Document sourceDoc) {
        List<JSONObject> tableOfContents = new ArrayList<>();
        try {
            // 获取文档中的所有段落
            NodeCollection paragraphs = sourceDoc.getChildNodes(NodeType.PARAGRAPH, true);

            log.info("开始提取文档目录，共找到 {} 个段落", paragraphs.getCount());

            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph para = (Paragraph) paragraphs.get(i);

                // 检查是否为标题段落
                if (isHeadingLevel(para)) {
                    JSONObject tocEntry = new JSONObject();
                    String title = para.getText().trim();

                    // 获取标题级别
                    int headingLevel = getHeadingLevel(para);

                    // 获取页码（如果有的话）
                    String pageNumber = extractPageNumber(para);

                    tocEntry.put("title", title);
                    tocEntry.put("level", headingLevel);
                    tocEntry.put("pageNumber", pageNumber);
                    tocEntry.put("index", i);

                    tableOfContents.add(tocEntry);

                    log.info("发现目录项: 级别{} - {} (页码: {})", headingLevel, title, pageNumber);
                }
            }

            log.info("目录提取完成，共找到 {} 个目录项", tableOfContents.size());

        } catch (Exception e) {
            log.error("提取文档目录时出错: {}", e.getMessage());
        }

        // 构建层级结构
        List<JSONObject> hierarchicalToc = buildHierarchicalStructure(tableOfContents);

        JSONObject result = new JSONObject();
        result.put("flatToc", tableOfContents);
        result.put("hierarchicalToc", hierarchicalToc);
        result.put("totalItems", tableOfContents.size());

        return result;
    }

    private List<JSONObject> createSplitDocuments(List<DocumentSection> sections, Document originalDoc, Integer templateId, Integer formdataId) throws Exception {
        log.info("开始创建 {} 个拆分文档", sections.size());
        List<JSONObject> result = new ArrayList<>();
        for (int i = 0; i < sections.size(); i++) {
            try {
                log.info("正在处理第 {} 个段落: {}", i + 1, sections.get(i).Title);

                // 创建新文档
                Document newDoc = new Document();
                newDoc.removeAllChildren();

                // 复制原文档的所有样式（包括表格样式）
                copyAllStyles(originalDoc, newDoc);

                // 创建新的段落
                Section newSection = new Section(newDoc);
                Body newBody = new Body(newDoc);
                newSection.appendChild(newBody);
                newDoc.appendChild(newSection);

                // 确定内容范围
                List<Node> contentNodes = getSectionContent(sections, i);
                log.info("段落包含 {} 个节点", contentNodes.size());

                // 复制内容到新文档
                for (Node node : contentNodes) {
                    try {
                        log.info("正在导入节点类型: {}", node.getNodeType());
                        Node importedNode = newDoc.importNode(node, true);
                        newBody.appendChild(importedNode);

                        if (node.getNodeType() == NodeType.TABLE) {
                            log.info("成功导入表格节点");
                        }
                    } catch (Exception ex) {
                        log.info("节点 {} 导入失败: {}", node.getNodeType(), ex.getMessage());
                        ex.printStackTrace();
                    }
                }

                // 生成文件名
                String fileName = sanitizeFileName(sections.get(i).Title + " - " + i);
                String filePath = Paths.get(path + File.separator + templateId + File.separator + formdataId, fileName + ".docx").toString();

                // 保存文档
                newDoc.save(filePath);
                log.info("已保存: {}", filePath);

                // 清理内存
                contentNodes.clear();
                contentNodes = null;

                JSONObject obj = new JSONObject();
                obj.put("fileName", fileName);
                obj.put("filePath", templateId + "/" + formdataId + "/" + fileName + ".docx");
                result.add(obj);
            } catch (Exception ex) {
                log.info("处理第 {} 个段落时出错: {}", i + 1, ex.getMessage());
                ex.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 复制所有样式（包括表格样式）
     */
    private void copyAllStyles(Document sourceDoc, Document targetDoc) {
        try {
            // 复制所有样式
            for (Style style : sourceDoc.getStyles()) {
                try {
                    // 检查目标文档中是否已存在该样式
                    boolean exists = false;
                    for (Style existingStyle : targetDoc.getStyles()) {
                        if (existingStyle.getName().equals(style.getName())) {
                            exists = true;
                            break;
                        }
                    }

                    if (!exists) {
                        targetDoc.getStyles().addCopy(style);
                        log.info("复制样式: {}", style.getName());
                    }
                } catch (Exception ex) {
                    log.info("样式{}复制失败: {}", style.getName(), ex.getMessage());
                }
            }

            // 复制列表定义（用于编号和项目符号）
            for (int i = 0; i < sourceDoc.getLists().getCount(); i++) {
                try {
                    targetDoc.getLists().addCopy(sourceDoc.getLists().get(i));
                } catch (Exception ex) {
                    log.info("列表定义复制失败: {}", ex.getMessage());
                }
            }

        } catch (Exception ex) {
            log.info("样式复制过程中出错: {}", ex.getMessage());
        }
    }

    private List<Node> getSectionContent(List<DocumentSection> sections, int currentIndex) {
        // 直接返回已经收集好的内容
        return sections.get(currentIndex).Content;
    }


    /**
     * 清理文件名
     */
    private String sanitizeFileName(String fileName) {
        fileName = fileName.replaceAll(" ", "");
        return fileName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5._-]", "_")
                .replaceAll("_{2,}", "_")
                .substring(0, Math.min(fileName.length(), 50));
    }

    /**
     * 获取文档目录页中的所有目录
     * 专门解析目录页中的目录项及其层级关系，返回层级结构数据
     *
     * @param file 上传的文档文件
     * @return 包含层级结构的JSON对象
     */
    public JSONObject getDocumentTableOfContents(MultipartFile file) {
        JSONObject result = new JSONObject();
        List<JSONObject> flatToc = new ArrayList<>();

        try {
            // 加载源文档
            Document sourceDoc = new Document(file.getInputStream());

            // 查找目录页
            List<Paragraph> tocPageParagraphs = findTableOfContentsPage(sourceDoc);

            if (tocPageParagraphs.isEmpty()) {
                log.warn("未找到目录页");
                result.put("flatToc", flatToc);
                result.put("hierarchicalToc", new ArrayList<>());
                result.put("totalItems", 0);
                return result;
            }

            log.info("找到目录页，共 {} 个段落", tocPageParagraphs.size());

            // 解析目录页中的目录项
            for (int i = 0; i < tocPageParagraphs.size(); i++) {
                Paragraph para = tocPageParagraphs.get(i);

                // 获取段落的纯文本内容，过滤域代码
                String text = extractCleanText(para);

                // 添加调试日志
                log.debug("处理段落 {}: 原始文本=[{}], 清理后文本=[{}]", i, para.getText(), text);

                // 跳过空行和"目次"标题行
                if (text.isEmpty() || text.equals("目次") || text.equals("目录") || text.equals("TABLEOFCONTENTS")) {
                    log.debug("跳过段落 {}: 空行或标题行", i);
                    continue;
                }

                // 解析目录项
                JSONObject tocEntry = parseTocEntry(text, i);
                if (tocEntry != null) {
                    flatToc.add(tocEntry);
                    log.info("解析目录项: 级别{} - {} (页码: {})",
                        tocEntry.getInteger("level"),
                        tocEntry.getString("title"),
                        tocEntry.getString("pageNumber"));
                } else {
                    log.debug("段落 {} 未被识别为目录项: [{}]", i, text);
                }
            }

            // 构建层级结构
            List<JSONObject> hierarchicalToc = buildHierarchicalStructure(flatToc);

            result.put("flatToc", flatToc);
            result.put("hierarchicalToc", hierarchicalToc);
            result.put("totalItems", flatToc.size());

            log.info("目录解析完成，共找到 {} 个目录项", flatToc.size());

        } catch (Exception e) {
            log.error("解析文档目录时出错: {}", e.getMessage());
            throw new RuntimeException("解析文档目录失败", e);
        }

        return result;
    }

    /**
     * 测试目录项识别（用于调试）
     */
    public void testTocPatternRecognition() {
        String[] testTexts = {
            "前言 ................................................................ II",
            "前  言 ................................................................ II",
            "1 范围 ................................................................ 1",
            "6.1 设备分级 ........................................................ 2",
            "附录 .................................................................. 4"
        };

        for (String text : testTexts) {
            boolean isValid = containsValidTocPattern(text);
            String pageNumber = extractTocPageNumber(text);
            String title = extractTocTitle(text, pageNumber);
            log.info("测试文本: [{}] -> 有效: {}, 页码: [{}], 标题: [{}]",
                text, isValid, pageNumber, title);
        }
    }

    /**
     * 提取段落的纯文本内容，过滤Word域代码和格式信息
     */
    private String extractCleanText(Paragraph para) {
        try {
            StringBuilder cleanText = new StringBuilder();

            // 遍历段落中的所有运行（Run）
            for (Run run : para.getRuns()) {
                String runText = run.getText();
                if (runText != null && !runText.trim().isEmpty()) {
                    cleanText.append(runText);
                }
            }

            String text = cleanText.toString();

            // 如果Run方式获取的文本为空，尝试直接获取段落文本
            if (text.trim().isEmpty()) {
                text = para.getText();
            }

            // 移除Word域代码
            text = text.replaceAll("HYPERLINK\\s+[^\\s]+", "");
            text = text.replaceAll("PAGEREF\\s+[^\\s]+", "");
            text = text.replaceAll("_Toc\\d+", "");
            text = text.replaceAll("\\\\h", "");
            text = text.replaceAll("\\\\\\*", "");
            text = text.replaceAll("MERGEFORMAT", "");

            // 移除域代码的开始和结束标记
            text = text.replaceAll("\\x13", ""); // 域开始标记
            text = text.replaceAll("\\x14", ""); // 域分隔符
            text = text.replaceAll("\\x15", ""); // 域结束标记

            // 移除多余的空格和特殊字符，但保留必要的空格
            text = text.replaceAll("\\s+", " ");
            text = text.trim();

            // 记录调试信息
            log.debug("段落文本提取: 原始=[{}], 清理后=[{}]", para.getText(), text);

            return text;

        } catch (Exception e) {
            log.debug("提取段落文本失败: {}", e.getMessage());
            String fallbackText = para.getText().trim();
            log.debug("使用备用文本: [{}]", fallbackText);
            return fallbackText;
        }
    }

    /**
     * 构建层级结构
     */
    private List<JSONObject> buildHierarchicalStructure(List<JSONObject> flatToc) {
        List<JSONObject> result = new ArrayList<>();
        List<JSONObject> stack = new ArrayList<>();

        for (JSONObject item : flatToc) {
            JSONObject tocItem = new JSONObject();
            tocItem.put("title", item.getString("title"));
            tocItem.put("pageNumber", item.getString("pageNumber"));
            tocItem.put("level", item.getInteger("level"));
            tocItem.put("children", new ArrayList<>());

            int currentLevel = item.getInteger("level");

            // 调整栈，确保当前项的父级在栈顶
            while (!stack.isEmpty() &&
                   stack.get(stack.size() - 1).getInteger("level") >= currentLevel) {
                stack.remove(stack.size() - 1);
            }

            if (stack.isEmpty()) {
                // 顶级项目
                result.add(tocItem);
            } else {
                // 添加到父级的children中
                JSONObject parent = stack.get(stack.size() - 1);
                parent.getJSONArray("children").add(tocItem);
            }

            stack.add(tocItem);
        }

        return result;
    }

    /**
     * 查找文档中的目录页段落
     */
    private List<Paragraph> findTableOfContentsPage(Document sourceDoc) {
        List<Paragraph> tocParagraphs = new ArrayList<>();
        try {
            NodeCollection paragraphs = sourceDoc.getChildNodes(NodeType.PARAGRAPH, true);
            boolean foundTocStart = false;
            boolean foundTocEnd = false;

            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph para = (Paragraph) paragraphs.get(i);
                String text = para.getText().trim().replaceAll(" ", "");

                // 查找目录页开始标志
                if (!foundTocStart && (text.equals("目次") || text.equals("目录") || text.equals("TABLE OF CONTENTS"))) {
                    foundTocStart = true;
                    tocParagraphs.add(para);
                    log.info("找到目录页开始: {}", text);
                    continue;
                }

                // 如果已经找到目录开始，继续收集段落
                if (foundTocStart && !foundTocEnd) {
                    tocParagraphs.add(para);

                    // 检查是否到达目录页结束（通常是遇到新的章节或页面）
                    if (isEndOfTocPage(text, para)) {
                        foundTocEnd = true;
                        log.info("目录页结束于: {}", text);
                        break;
                    }
                }
            }

            // 如果没有找到明确的结束标志，但找到了开始，则取一定数量的段落
            if (foundTocStart && !foundTocEnd && tocParagraphs.size() > 50) {
                tocParagraphs = tocParagraphs.subList(0, 50); // 限制在50个段落内
            }

        } catch (Exception e) {
            log.error("查找目录页时出错: {}", e.getMessage());
        }

        return tocParagraphs;
    }

    /**
     * 判断是否到达目录页结束
     */
    private boolean isEndOfTocPage(String text, Paragraph para) {
        // 如果遇到明显的章节开始标志
        if (text.matches("^第[一二三四五六七八九十\\d]+章.*") ||
            text.matches("^第[一二三四五六七八九十\\d]+节.*") ||
            text.matches("^[1-9]\\d*\\..*") ||
            text.matches("^前\\s*言.*") ||
            text.matches("^序\\s*言.*")) {
            return true;
        }

        // 检查段落样式是否为正文样式（非目录样式）
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style != null) {
                String styleName = style.getName().toLowerCase();
                if (styleName.contains("normal") || styleName.contains("正文")) {
                    return true;
                }
            }
        } catch (Exception e) {
            // 忽略样式检查错误
        }

        return false;
    }

    /**
     * 解析目录项文本，提取标题、级别和页码
     */
    private JSONObject parseTocEntry(String text, int index) {
        try {
            // 添加调试日志
            log.debug("解析目录项 {}: 文本=[{}], 长度={}, 是否匹配模式={}",
                index, text, text.length(), containsValidTocPattern(text));

            // 跳过明显不是目录项的文本
            if (text.length() < 2 || !containsValidTocPattern(text)) {
                log.debug("目录项 {} 被跳过: 长度不足或不匹配模式", index);
                return null;
            }

            JSONObject tocEntry = new JSONObject();

            // 解析层级关系（通过缩进或编号判断）
            int level = determineTocLevel(text);

            // 提取页码（通常在行末）
            String pageNumber = extractTocPageNumber(text);

            // 提取标题（去除页码和点线）
            String title = extractTocTitle(text, pageNumber);

            if (title.isEmpty()) {
                return null;
            }

            tocEntry.put("title", title);
            tocEntry.put("level", level);
            tocEntry.put("pageNumber", pageNumber);
            tocEntry.put("index", index);
            tocEntry.put("originalText", text);

            return tocEntry;

        } catch (Exception e) {
            log.debug("解析目录项失败: {} - {}", text, e.getMessage());
            return null;
        }
    }

    /**
     * 检查文本是否包含有效的目录模式
     */
    private boolean containsValidTocPattern(String text) {
        // 记录调试信息
        log.debug("检查目录模式: [{}]", text);

        // 先检查特殊的目录项（前言、序言等）- 更宽松的匹配
        if (text.contains("前言") || text.contains("序言") ||
            text.matches(".*前\\s*言.*") || text.matches(".*序\\s*言.*")) {
            log.debug("匹配特殊目录项: 前言/序言");
            return true;
        }

        // 检查是否包含页码（阿拉伯数字）
        if (text.matches(".*\\d+\\s*$")) {
            log.debug("匹配阿拉伯数字页码");
            return true;
        }

        // 检查是否包含罗马数字页码
        if (text.matches(".*[IVX]+\\s*$")) {
            log.debug("匹配罗马数字页码");
            return true;
        }

        // 检查是否包含目录常见的编号模式
        if (text.matches("^\\s*\\d+.*") || // 数字开头
            text.matches("^\\s*\\d+\\.\\d+.*") || // 如 6.1
            text.matches("^\\s*[一二三四五六七八九十]+.*") || // 中文数字
            text.matches("^\\s*第[一二三四五六七八九十\\d]+章.*") || // 第X章
            text.matches("^\\s*附录.*")) { // 附录
            log.debug("匹配编号模式");
            return true;
        }

        // 检查是否包含点线（目录中常见的连接线）
        if (text.contains("…") || text.contains("....") || text.contains("···") ||
            text.contains("．．．") || text.contains("..")) {
            log.debug("匹配点线连接符");
            return true;
        }

        // 检查是否包含常见的目录关键词
        if (text.matches(".*(?:范围|引用|定义|总则|职责|要求|管理|内容|方法|程序|流程|记录|报告).*")) {
            log.debug("匹配目录关键词");
            return true;
        }

        // 更宽松的匹配：如果文本长度合理且包含中文字符，也认为可能是目录项
        if (text.length() >= 2 && text.length() <= 100 && text.matches(".*[\\u4e00-\\u9fa5].*")) {
            log.debug("匹配中文内容（宽松模式）");
            return true;
        }

        log.debug("未匹配任何目录模式");
        return false;
    }

    /**
     * 确定目录项的层级
     */
    private int determineTocLevel(String text) {
        // 通过前导空格数量判断层级
        int leadingSpaces = 0;
        for (char c : text.toCharArray()) {
            if (c == ' ' || c == '\t') {
                leadingSpaces++;
            } else {
                break;
            }
        }

        String trimmedText = text.trim();

        // 根据编号模式判断层级
        if (trimmedText.matches("^\\d+\\s+.*")) { // 如 "1 范围"
            return 1;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\s+.*")) { // 如 "6.1 设备分级"
            return 2;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\.\\d+\\s+.*")) { // 如 "6.1.1 xxx"
            return 3;
        } else if (trimmedText.matches("^\\d+\\.\\d+\\.\\d+\\.\\d+\\s+.*")) { // 如 "6.1.1.1 xxx"
            return 4;
        } else if (trimmedText.matches("^第[一二三四五六七八九十\\d]+章.*")) { // 第X章
            return 1;
        } else if (trimmedText.matches("^附录.*")) { // 附录
            return 1;
        } else if (leadingSpaces > 10) { // 较多缩进
            return 3;
        } else if (leadingSpaces > 5) { // 中等缩进
            return 2;
        }

        return 1; // 默认为1级
    }

    /**
     * 从目录项中提取页码
     */
    private String extractTocPageNumber(String text) {
        // 匹配行末的数字（页码）
        if (text.matches(".*\\d+\\s*$")) {
            String[] parts = text.trim().split("\\s+");
            String lastPart = parts[parts.length - 1];
            if (lastPart.matches("\\d+")) {
                return lastPart;
            }
        }

        // 匹配罗马数字页码（如 II, III）
        if (text.matches(".*[IVX]+\\s*$")) {
            String[] parts = text.trim().split("\\s+");
            String lastPart = parts[parts.length - 1];
            if (lastPart.matches("[IVX]+")) {
                return lastPart;
            }
        }

        return "";
    }

    /**
     * 从目录项中提取标题
     */
    private String extractTocTitle(String text, String pageNumber) {
        String title = text.trim();

        // 移除页码（阿拉伯数字和罗马数字）
        if (!pageNumber.isEmpty()) {
            title = title.replaceAll("\\s+" + pageNumber + "\\s*$", "");
            // 处理罗马数字页码的特殊情况
            title = title.replaceAll(pageNumber + "\\s*$", "");
        }

        // 移除各种类型的点线连接符
        title = title.replaceAll("[…\\.]{3,}", " ");
        title = title.replaceAll("·{3,}", " ");
        title = title.replaceAll("．{3,}", " ");
        title = title.replaceAll("\\s*\\.{4,}\\s*", " ");

        // 特殊处理"前言"等词语中的空格
        title = title.replaceAll("前\\s+言", "前言");
        title = title.replaceAll("序\\s+言", "序言");

        // 清理多余的空格
        title = title.replaceAll("\\s+", " ").trim();

        return title;
    }

    /**
     * 获取标题的级别
     */
    private int getHeadingLevel(Paragraph para) {
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style == null) {
                return 1;
            }

            String styleName = style.getName().toLowerCase();

            // 尝试从样式名称中提取级别
            if (styleName.matches(".*[1-6].*")) {
                for (int i = 1; i <= 6; i++) {
                    if (styleName.contains(String.valueOf(i))) {
                        return i;
                    }
                }
            }

            // WPS Office的数字样式ID映射
            if (styleName.equals("4")) return 1;
            if (styleName.equals("5")) return 2;
            if (styleName.equals("6")) return 3;
            if (styleName.equals("7")) return 4;
            if (styleName.equals("8")) return 5;
            if (styleName.equals("9")) return 6;

            // 默认返回1级标题
            return 1;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * 从段落中提取页码信息
     */
    private String extractPageNumber(Paragraph para) {
        try {
            String text = para.getText();

            // 查找页码模式，通常在目录中以数字结尾
            if (text.matches(".*\\d+\\s*$")) {
                String[] parts = text.trim().split("\\s+");
                String lastPart = parts[parts.length - 1];
                if (lastPart.matches("\\d+")) {
                    return lastPart;
                }
            }

            // 查找制表符后的数字（目录中常见的格式）
            if (text.contains("\t")) {
                String[] tabParts = text.split("\t");
                String lastTabPart = tabParts[tabParts.length - 1].trim();
                if (lastTabPart.matches("\\d+")) {
                    return lastTabPart;
                }
            }

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 检查是否为标题样式
     */
    private boolean isHeadingLevel(Paragraph para) {
        try {
            Style style = para.getParagraphFormat().getStyle();
            if (style == null) {
                return false;
            }

            String styleName = style.getName().toLowerCase();

            // 1. 匹配WPS Office的数字样式ID（4=标题1, 5=标题2, 6=标题3, 7=标题4）
            if (styleName.matches("[4-9]") ||
                    styleName.matches("heading\\s*[1-6]") ||
                    styleName.matches(".*标题\\s*[1-6].*") ||
                    styleName.matches(".*[一级二三四五六]级标题.*") ||
                    styleName.matches(".*title\\s*[1-6].*") ||
                    styleName.matches("heading[1-6]") ||
                    styleName.matches(".*标题[1-6].*")) {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 合并文档
     *
     * @param templateId
     * @param formdataId
     */
    public void mergeDocuments(Integer templateId, Integer formdataId) {
        try {
            String sourcePath = path + File.separator + templateId + File.separator + formdataId;
            String targetPath = path + File.separator + templateId;

            File dir = new File(sourcePath);
            File[] docxFiles = dir.listFiles((d, name) -> name.toLowerCase().endsWith(".docx"));
            if (docxFiles == null || docxFiles.length == 0) {
                log.info("未找到任何.docx文件");
                return;
            }

            // 按文件名排序，确保合并顺序正确
            Arrays.sort(docxFiles, Comparator.comparing((File file) -> {
                String name = file.getName();
                int dashIndex = name.lastIndexOf("-");
                if (dashIndex != -1 && dashIndex < name.length() - 1) {
                    try {
                        String numStr = name.substring(dashIndex + 1, name.lastIndexOf("."));
                        return Integer.parseInt(numStr);
                    } catch (NumberFormatException e) {
                        // 如果解析失败，按文件名排序
                        return Integer.MAX_VALUE;
                    }
                }
                return Integer.MAX_VALUE;
            }));

            // 打印排序后的文件列表
            log.info("找到以下文档，将按以下顺序合并：");
            for (int i = 0; i < docxFiles.length; i++) {
                System.out.println((i + 1) + ". " + docxFiles[i].getName());
                log.info("{}.{}", i + 1, docxFiles[i].getName());
            }

            // 初始化 Document 对象
            Document doc = new Document(docxFiles[0].getAbsolutePath());
            log.info("基础文档: {}", docxFiles[0].getName());

            // 从第二个文件开始合并（跳过第一个，因为它已经是基础文档）
            for (int i = 1; i < docxFiles.length; i++) {
                Document document = new Document(docxFiles[i].getAbsolutePath());
                System.out.println("正在合并: " + docxFiles[i].getName());

                // 判断是否需要分页：目录(0)和前言(1)之间需要分页
                boolean needsPageBreak = (i == 1); // 只在前言文档前添加分页

                // 使用改进的合并方法
                appendDocument(doc, document, needsPageBreak);
            }

            // 保存合并的文档，使用时间戳避免文件占用
            String finalOutputPath = targetPath + File.separator + "result.docx";
            doc.save(finalOutputPath);
            System.out.println("文档合并完成，保存至: " + finalOutputPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void appendDocument(Document dstDoc, Document srcDoc, boolean needsPageBreak) throws Exception {
        if (needsPageBreak) {
            // 保留分页符：设置为新页开始
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.NEW_PAGE);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.KEEP_SOURCE_FORMATTING);
        } else {
            // 连续合并：设置为连续模式，避免分页
            srcDoc.getFirstSection().getPageSetup().setSectionStart(SectionStart.CONTINUOUS);
            dstDoc.appendDocument(srcDoc, ImportFormatMode.USE_DESTINATION_STYLES);
        }
    }


    public static void registerWord2412() {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
