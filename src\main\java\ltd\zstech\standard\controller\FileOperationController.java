package ltd.zstech.standard.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import ltd.zstech.common.api.vo.Result;
import ltd.zstech.common.aspect.annotation.AutoLog;
import ltd.zstech.modules.commonservice.service.FormOperationService;
import ltd.zstech.modules.commonservice.service.ICustomFormTemplateService;
import ltd.zstech.modules.views.entity.FormCustomFormTemplate;
import ltd.zstech.standard.entity.FileDetailVo;
import ltd.zstech.standard.request.FileSplitRequest;
import ltd.zstech.standard.service.FileOperationService;
import ltd.zstech.standard.util.DuokeAPI;
import ltd.zstech.standard.util.MultiUserTokenManager;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/fileOperation")
@Slf4j
public class FileOperationController {

    @Resource
    private FormOperationService formOperationService;
    @Resource
    private ICustomFormTemplateService customFormTemplateService;
    @Resource
    private DuokeAPI duokeAPI;
    @Resource
    private MultiUserTokenManager multiUserTokenManager;
    @Resource
    private FileOperationService fileOperationService;

    @PostMapping(value = "/split")
    @ApiOperation(value = "文档结构化")
    @AutoLog
    public Result<?> splitDocument(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        Integer subTemplateId = request.getSubTemplateId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }
        Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
        // 获取文件id，根据文件id从多可获取到文件地址
        String fid = formById.get("fid").toString();
        // String fid = "X00035";
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);
        FileDetailVo fileDetail = duokeAPI.getFileDetail(accessToken, fid);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            // MultipartFile file = new MockMultipartFile("file", "QSN—WJ—1—101—2024设备缺陷管理制度.docx", "text/plain", stream);
            MultipartFile file = new MockMultipartFile("file", fileDetail.getName(), "text/plain", stream);
            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }
            // 切分后的文件上传到服务器
            List<JSONObject> jsonObjects = fileOperationService.splitDocumentByHeadings(file, templateId, formdataId);
            // 拆分成功之后，对目次进行映射：
            FormCustomFormTemplate subFormTemplate = customFormTemplateService.getById(subTemplateId);
            List<Map<String, Object>> formData = formOperationService.getFormData(subFormTemplate.getFormtablename(), "pid=" + formdataId);
            for (JSONObject jsonObject : jsonObjects) {
                String fileName = jsonObject.getString("fileName");
                String filePath = jsonObject.getString("filePath");

                for (Map<String, Object> formDatum : formData) {
                    String mcmc = formDatum.get("Mcmc").toString();
                    fileName = getFileNameWithoutExtension(fileName);
                    if (fileName.equals(mcmc)) {
                        formDatum.put("WJ", filePath);
                        Object id = formDatum.get("ID");
                        formDatum.remove("ID");
                        formOperationService.update(subFormTemplate.getFormtablename(), "ID=" + id, formDatum);
                    }
                }
            }

            // 切分完成之后，更新制度状态为待发布
            formById.put("Zt", "待发布");
            Object id = formById.get("ID");
            formById.remove("ID");
            formOperationService.update(formTemplate.getFormtablename(), "ID=" + id, formById);

            // 切分后进行合并
            // fileOperationService.mergeDocuments(templateId, formdataId);
            return Result.ok("文档结构化完成");
        } catch (Exception e) {
            log.error("文档结构化失败", e);
            return Result.error("文档结构化失败: " + e.getMessage());
        }
    }

    @PostMapping(value = "/splitWithTOC")
    @ApiOperation(value = "文档结构化（包含目录信息）")
    @AutoLog
    public Result<?> splitDocumentWithTOC(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        Integer subTemplateId = request.getSubTemplateId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }
        Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
        // 获取文件id，根据文件id从多可获取到文件地址
        String fid = formById.get("fid").toString();
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);
        FileDetailVo fileDetail = duokeAPI.getFileDetail(accessToken, fid);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile("file", fileDetail.getName(), "text/plain", stream);
            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }

            // 使用新的方法获取拆分结果和目录信息
            JSONObject result = fileOperationService.splitDocumentByHeadingsWithTOC(file, templateId, formdataId);
            List<JSONObject> splitDocuments = result.getJSONArray("splitDocuments").toJavaList(JSONObject.class);
            List<JSONObject> tableOfContents = result.getJSONArray("tableOfContents").toJavaList(JSONObject.class);

            // 拆分成功之后，对目次进行映射：
            FormCustomFormTemplate subFormTemplate = customFormTemplateService.getById(subTemplateId);
            List<Map<String, Object>> formData = formOperationService.getFormData(subFormTemplate.getFormtablename(), "pid=" + formdataId);
            for (JSONObject jsonObject : splitDocuments) {
                String fileName = jsonObject.getString("fileName");
                String filePath = jsonObject.getString("filePath");

                for (Map<String, Object> formDatum : formData) {
                    String mcmc = formDatum.get("Mcmc").toString();
                    fileName = getFileNameWithoutExtension(fileName);
                    if (fileName.equals(mcmc)) {
                        formDatum.put("WJ", filePath);
                        Object id = formDatum.get("ID");
                        formDatum.remove("ID");
                        formOperationService.update(subFormTemplate.getFormtablename(), "ID=" + id, formDatum);
                    }
                }
            }

            // 切分完成之后，更新制度状态为待发布
            formById.put("Zt", "待发布");
            Object id = formById.get("ID");
            formById.remove("ID");
            formOperationService.update(formTemplate.getFormtablename(), "ID=" + id, formById);

            // 返回包含目录信息的结果
            JSONObject response = new JSONObject();
            response.put("message", "文档结构化完成");
            response.put("splitDocuments", splitDocuments);
            response.put("tableOfContents", tableOfContents);
            response.put("totalSections", result.getInteger("totalSections"));

            return Result.ok(response);
        } catch (Exception e) {
            log.error("文档结构化失败", e);
            return Result.error("文档结构化失败: " + e.getMessage());
        }
    }

    @PostMapping(value = "/getTableOfContents")
    @ApiOperation(value = "获取文档目录信息")
    @AutoLog
    public Result<?> getTableOfContents(@RequestBody FileSplitRequest request) {
        Integer templateId = request.getTemplateId();
        Integer formdataId = request.getFormdataId();
        FormCustomFormTemplate formTemplate = customFormTemplateService.getById(templateId);
        if (formTemplate == null) {
            return Result.error500("模板不存在");
        }
        Map<String, Object> formById = formOperationService.getFormById(formTemplate.getFormtablename(), String.valueOf(formdataId));
        // 获取文件id，根据文件id从多可获取到文件地址
        String fid = formById.get("fid").toString();
        String accessToken = multiUserTokenManager.getAccessToken("developer", "b876063594f8e2b3359ddfc10e8c615f");
        String downloadUrl = duokeAPI.downloadFile(accessToken, fid).get(0);
        FileDetailVo fileDetail = duokeAPI.getFileDetail(accessToken, fid);

        try {
            byte[] bytes = HttpRequest.post(downloadUrl).execute().bodyBytes();
            InputStream stream = new ByteArrayInputStream(bytes);
            MultipartFile file = new MockMultipartFile("file", fileDetail.getName(), "text/plain", stream);
            if (!isWordDocument(file)) {
                return Result.error("只支持Word文档格式 (.docx)");
            }

            // 获取文档目录信息
            List<JSONObject> tableOfContents = fileOperationService.getDocumentTableOfContents(file);

            JSONObject response = new JSONObject();
            response.put("tableOfContents", tableOfContents);
            response.put("totalItems", tableOfContents.size());

            return Result.ok(response);
        } catch (Exception e) {
            log.error("获取文档目录失败", e);
            return Result.error("获取文档目录失败: " + e.getMessage());
        }
    }

    private boolean isWordDocument(MultipartFile file) {
        String contentType = file.getContentType();
        String fileName = file.getOriginalFilename();

        return (contentType != null &&
                contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document"))
                || (fileName != null && fileName.toLowerCase().endsWith(".docx"));
    }

    private String getFileNameWithoutExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf("-");
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        } else {
            return fileName;
        }
    }
}
